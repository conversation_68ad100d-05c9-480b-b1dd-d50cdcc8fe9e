<script setup lang="ts">
import Vditor from "vditor";
import "vditor/dist/index.css";
import {
  ref,
  onMounted,
  defineProps,
  defineExpose,
  defineModel,
  watch,
  withDefaults,
} from "vue";
import { ElMessage } from "element-plus";
import { http } from "@/apis";
import {
  safeFilename,
  isFileSizeExceeded,
  revertMathScriptsToMd,
  unescapeLatexFormulas,
} from "./utils";
import { convertLanguageMathToScript } from "@/utils/latexUtils";
interface VeditorInlineProps {
  disabled?: boolean;
  height?: number;
  mode?: "ir" | "wysiwyg" | "sv";
  placeholder?: string;
  showToolbar?: boolean; // 控制工具栏是否显示
  autoHeight?: boolean; // 是否自动调整高度
  maxHeight?: number; // 最大高度限制
  counterMax?: number; // 字符计数器最大值限制
}

const props = withDefaults(defineProps<VeditorInlineProps>(), {
  disabled: false,
  height: 150, // inline版本默认高度更小
  mode: "ir", //
  placeholder: "请输入",
  showToolbar: false, // 默认不显示工具栏
  autoHeight: false, // 默认不自动调整高度
  maxHeight: 100, // 默认最大高度100px
  // counterMax 不设置默认值，只有明确传入时才启用计数器
});

const vditor = ref<Vditor>();
const model = defineModel<string>();
const isReady = ref(false);

// 添加唯一ID生成
const editorId = ref(`vditor-${Math.random().toString(36).substring(2, 11)}`);

// 优化的滚动条控制 - 防止闪烁的最佳方案
const checkContentOverflow = () => {
  if (!props.autoHeight || !vditor.value) return;

  const editor = document.getElementById(editorId.value);
  const contentElement = editor?.querySelector(
    ".vditor-content"
  ) as HTMLElement;

  if (contentElement) {
    // 使用双重延迟确保DOM完全更新
    requestAnimationFrame(() => {
      setTimeout(() => {
        const scrollHeight = contentElement.scrollHeight;
        const maxHeight = props.maxHeight;

        if (scrollHeight > maxHeight) {
          contentElement.classList.add("content-overflow");
        } else {
          contentElement.classList.remove("content-overflow");
        }
      }, 10); // 额外10ms延迟确保稳定
    });
  }
};

watch(
  () => model.value,
  (newVal) => {
    if (vditor.value && isReady.value && newVal !== vditor.value.getValue()) {
      vditor.value.setValue(newVal || "");
      // 内容变化后检查滚动条状态
      if (props.autoHeight) {
        checkContentOverflow();
      }
    }
  },
  { deep: true }
);

onMounted(() => {
  if (model.value) {
    model.value += " ";
  }

  vditor.value = new Vditor(editorId.value, {
    // 使用动态生成的ID
    height: props.autoHeight ? "auto" : props.height,
    width: "100%",
    mode: props.mode,
    cache: {
      enable: false,
    },
    minHeight: props.autoHeight ? props.height : undefined,
    resize: {
      enable: false,
    },
    preview: {
      math: {
        engine: "KaTeX",
      },
      markdown: {
        sanitize: true,
        autoSpace: true,
        paragraphBeginningSpace: true,
      },
    },
    toolbar: props.showToolbar
      ? [
          "headings",
          "bold",
          "italic",
          "strike",
          "link",
          "|",
          "list",
          "ordered-list",
          "check",
          "outdent",
          "indent",
          "|",
          "quote",
          "line",
          "code",
          "inline-code",
          "table",
          "edit-mode",
          "both",
          // "|",
          "upload",

          // "|",
          // "undo",
          // "redo",
          // "|",

          // "preview",
          // "outline",
          // "code-theme",
          // "export",
        ]
      : [],
    hint: {
      extend: [], // 禁用扩展提示
    },
    comment: {
      enable: false, // 禁用评论功能
    },
    counter: {
      enable: props.counterMax !== undefined, // 只有传入 counterMax 参数时才启用计数器
      max: props.counterMax || 1800, // 如果启用但未设置值，使用默认值 1800
      type: "markdown",
    },
    // 配置图片上传
    upload: {
      // 上传图片时的回调函数
      accept: "image/jpeg,image/png,image/gif,image/jpg,image/bmp", // 图片格式
      max: 2 * 1024 * 1024, // 控制大小 (2MB)
      multiple: true, // 是否允许批量上传
      linkToImgUrl: "/cos/file/imageCosUploadF",
      handler: async (files: File[]) => {
        try {
          // 创建一个数组来存储所有上传成功的图片信息
          const uploadResults = [];

          // 遍历对每个文件单独上传
          for (const file of files) {
            // 在try块外定义安全文件名，确保catch块中可以访问
            let safeFileName = "";
            try {
              // 对文件名进行安全处理
              safeFileName = safeFilename(file.name);

              // 检查文件大小是否超过限制
              if (isFileSizeExceeded(file)) {
                console.error(`文件 ${safeFileName} 大小超过限制:`, file.size);

                // 显示上传失败通知
                ElMessage({
                  message: `图片 ${safeFileName} 上传失败：文件大小超过2MB限制`,
                  type: "error",
                  duration: 3000,
                });

                uploadResults.push({
                  filename: safeFileName,
                  success: false,
                  error: "文件大小超过2MB限制",
                });

                continue; // 跳过此文件的上传
              }

              // 创建一个新的File对象，使用安全处理后的文件名
              const safeFile = new File([file], safeFileName, {
                type: file.type,
              });

              const formData = new FormData();
              // 注意：后端接口期望的是"upload"作为key，与 CKEditor 保持一致
              formData.append("upload", safeFile);

              // 上传接口 - 每个文件单独调用，并设置更长的超时时间
              const response = await http<any>({
                url: "/cos/file/imageCosUploadF",
                method: "post",
                data: formData,
              });

              // 处理响应，提取URL
              let imageUrl = "";
              if (response.data) {
                imageUrl = response.data.url;
              }

              if (imageUrl) {
                console.log("插入图片URL:", imageUrl);

                // 使用已经安全处理过的文件名
                const filename = safeFileName;

                // 无论什么模式都使用Markdown格式插入图片
                const insertContent = `![${filename}](${imageUrl})\n`;

                // 将内容插入到编辑器
                vditor.value?.insertValue(insertContent);

                // 显示上传成功通知
                ElMessage({
                  message: `图片 ${filename} 上传成功`,
                  type: "success",
                  duration: 2000,
                });

                // 保存上传结果
                uploadResults.push({
                  filename,
                  url: imageUrl,
                  success: true,
                });
              } else {
                console.error("上传成功但未获取到图片URL:", response);

                // 显示上传失败通知
                ElMessage({
                  message: `图片 ${safeFileName} 上传失败：未获取到图片URL`,
                  type: "error",
                  duration: 3000,
                });

                uploadResults.push({
                  filename: safeFileName,
                  success: false,
                  error: "上传成功但未获取到图片URL",
                });
              }
            } catch (error) {
              // 如果safeFileName为空，说明在安全处理文件名时失败，需要重新处理
              if (!safeFileName) {
                safeFileName = safeFilename(file.name);
              }
              console.error(`文件 ${safeFileName} 上传失败:`, error);

              // 显示上传失败通知
              let errorMessage = "上传失败";
              if (error instanceof Error) {
                errorMessage = error.message;
                // 如果是超时错误，提供更有用的提示
                if (errorMessage.includes("timeout")) {
                  errorMessage = `上传超时，请检查网络连接或尝试分批上传`;
                }
              }

              ElMessage({
                message: `图片 ${safeFileName} 上传失败：${errorMessage}`,
                type: "error",
                duration: 3000,
              });

              uploadResults.push({
                filename: safeFileName,
                success: false,
                error: errorMessage,
              });
            }
          }

          console.log("所有文件上传结果:", uploadResults);
          // 返回空字符串，表示我们已经手动处理了图片插入，不需要Vditor再次处理
          return "";
        } catch (error) {
          console.error("Vditor图片上传错误:", error);

          // 处理错误并显示通知
          let errorMessage = "上传出错";
          if (error instanceof Error) {
            errorMessage = error.message;
            // 如果是超时错误，提供更有用的提示
            if (errorMessage.includes("timeout")) {
              errorMessage = `上传超时，请检查网络连接或尝试分批上传`;
            }
          }

          ElMessage({
            message: `图片上传失败：${errorMessage}`,
            type: "error",
            duration: 3000,
          });

          return JSON.stringify({
            msg: errorMessage,
            code: 1,
            data: {
              errFiles: files.map((file) => file.name),
              succMap: {},
            },
          });
        }
      },
    },
    after: () => {
      isReady.value = true;
      if (model.value) {
        vditor.value?.setValue(model.value.trim());
      }

      // 初始化时检查内容溢出状态
      if (props.autoHeight) {
        // 延迟执行，确保Vditor完全初始化
        setTimeout(() => {
          checkContentOverflow();
        }, 150);
      }

      // 修改事件监听，使用动态ID，只有在显示工具栏时才添加事件监听
      if (props.showToolbar) {
        const editor = document.getElementById(editorId.value);
        if (editor) {
          editor.addEventListener("click", () => {
            editor.classList.add("toolbar-visible");
          });

          editor.addEventListener(
            "blur",
            (event) => {
              const toolbar = editor.querySelector(".vditor-toolbar");
              if (toolbar && !toolbar.contains(event.relatedTarget as Node)) {
                editor.classList.remove("toolbar-visible");
              }
            },
            true
          );
        }
      }
    },
    input: (value: string) => {
      model.value = value;
      // 内容变化时检查是否需要显示滚动条
      if (props.autoHeight) {
        checkContentOverflow();
      }
    },
    placeholder: props.placeholder,
  });

  if (props.disabled) {
    vditor.value.disabled();
  }
});

const getData = (): string => {
  if (vditor.value) {
    return vditor.value.getValue();
  }
  return "";
};

const setData = (value: string, dis?: boolean) => {
  if (vditor.value && isReady.value) {
    vditor.value.setValue(value);
    if (dis !== undefined) {
      if (dis) {
        vditor.value.disabled();
      } else {
        vditor.value.enable();
      }
    }
  }
};
const getHtml = (): string => {
  if (vditor.value) {
    return convertLanguageMathToScript(vditor.value.getHTML());
  }
  return "";
};

const html2Md = (html: string): string => {
  if (vditor.value && isReady.value) {
    return unescapeLatexFormulas(
      revertMathScriptsToMd(vditor.value.html2md(html))
    );
  }
  return "";
};

// 延迟转换HTML为Markdown的方法
const html2MdWhenReady = (html: string): Promise<string> => {
  return new Promise((resolve) => {
    if (vditor.value && isReady.value) {
      // 如果已经就绪，立即转换
      console.log("html", html);
      let result = vditor.value.html2md(revertMathScriptsToMd(html));
      // 处理LaTeX公式转义问题
      result = unescapeLatexFormulas(result);
      // console.log('result', result);
      resolve(result);
    } else {
      // 如果未就绪，等待就绪后转换
      const checkReady = () => {
        if (vditor.value && isReady.value) {
          console.log("html", html);

          let result = vditor.value.html2md(revertMathScriptsToMd(html));
          // 处理LaTeX公式转义问题
          result = unescapeLatexFormulas(result);
          console.log("result", result);

          resolve(result);
        } else {
          setTimeout(checkReady, 100);
        }
      };
      checkReady();
    }
  });
};
defineExpose({
  getData,
  setData,
  getHtml,
  html2MdWhenReady,
});
</script>

<template>
  <!-- 使用动态生成的ID -->
  <div
    :id="editorId"
    class="vditor-inline-container"
    :data-auto-height="props.autoHeight"
  ></div>
</template>

<style scoped>
.vditor-inline-container {
  border: 1px solid rgb(220, 223, 230);
  border-radius: 2px;
  position: relative;
}

/* 自适应高度时的样式 - 优化方案 */
.vditor-inline-container[data-auto-height="true"] :deep(.vditor-content) {
  margin-top: 0;
  padding: 0 !important;
  min-height: v-bind('props.height + "px"') !important;
  max-height: v-bind('props.maxHeight + "px"') !important;
  height: auto !important;
  overflow-x: hidden;
  /* 默认隐藏滚动条，防止闪烁 */
  overflow-y: hidden;
  transition: all 0.15s ease;
}

/* 当内容确实超出时才显示滚动条 */
.vditor-inline-container[data-auto-height="true"]
  :deep(.vditor-content.content-overflow) {
  overflow-y: auto;
}

/* 固定高度时的样式 */
.vditor-inline-container[data-auto-height="false"] :deep(.vditor-content) {
  margin-top: 0;
  padding: 0 !important;
  min-height: unset !important;
  height: v-bind('props.height + "px"') !important;
  overflow: hidden;
}

/* 重置内容区域样式 */
:deep(.vditor-reset) {
  padding: 5px 10px !important;
  width: 100% !important;
  p {
    margin-bottom: 0px;
  }
  /* 修复列表样式 */
  & ul {
    list-style-type: disc !important;
    padding-left: 20px !important;
  }

  & ol {
    list-style-type: decimal !important;
    padding-left: 20px !important;
  }

  /* 修复列表项标记 */
  & li::marker {
    content: initial !important;
    color: initial !important;
  }
}

/* 移除所有滚动条相关样式 */

/* 工具栏样式保持不变 */
:deep(.vditor-toolbar) {
  position: absolute;
  left: -1px;
  right: -1px;
  bottom: 100%;
  z-index: 1000;
  background-color: #fff;
  border: 1px solid #ccced1;
  border-bottom: none;
  border-radius: 5px 5px 0 0;
  opacity: 0;
  visibility: hidden;
  transform: translateY(10px);
  transition: all 0.3s ease;
  margin-bottom: -1px;
}

.toolbar-visible :deep(.vditor-toolbar) {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.toolbar-visible {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

/* 添加以下样式来隐藏面板 */
:deep(.vditor-panel) {
  display: none !important;
}
</style>
